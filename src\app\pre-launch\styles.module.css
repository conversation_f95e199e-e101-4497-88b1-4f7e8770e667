
/* FIRST FRAME - Blue background with logos and characters */
.firstFrame {
  background: linear-gradient(180deg, #2850FF 0%, #FFF 93.45%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 40px 20px;
}

.logoX{
  color: white;
  font-weight: bolder;
  font-size: 3rem;
}

.headerLogo {
     display: flex;
    gap: 1rem;
    justify-content: center;
    align-items: center;
    margin-bottom: 2rem;
}

.charactersSection {
  display: flex;
  justify-content: center;
  align-items: center;
}


/* SECOND FRAME - White background with text */
.secondFrame {
  background: #FFFFFF;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 80px 20px;
  text-align: left;
}

.mainHeading {
  font-size: 4rem;
  font-weight: 700;
  font-family: var(--font-poppins);
  color: #EA73C0;
  margin: 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  text-align: left;
}

.subHeading {
  font-size: 1.8rem;
  font-family: var(--font-poppins);
  margin: 0;
  color: #949494;
  margin-bottom: 30px;
  text-align: left;
  font-weight: 500;
}

.description {
  font-size: 1.4rem;
  color: #86868B;
  font-family: var(--font-poppins);
  line-height: 1.8;
  margin: 0;
  width: 100%;
  text-align: left;
  font-weight: 600;
}

/* Teaser Videos Section */
.teaserTrailerHeading {
  font-size: 3rem;
  font-weight: 700;
  font-family: var(--font-poppins);
  margin: 40px 0 20px 0;
  text-align: center;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.teaserVideosContainer {
  display: flex;
  gap: 10px;
  width: 100%; /* fix from max-content to 100% */
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
  justify-content: flex-start;
  box-sizing: border-box; /* ensures padding doesn't overflow */
}

.teaserVideosContainer::-webkit-scrollbar {
  display: none;
}

.trailerVideoContainer {
  width: 100%;
  height: 746px;
  border: none; 
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    flex-shrink: 0;
}

.teaserVideoCard {
    border: none; 
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    width: 36rem;
    height: 20rem;
    flex-shrink: 0;
}


/* THIRD FRAME - Game screenshots */
.thirdFrame {
  padding: 0 0 80px 0;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none; 
}

.gamesContainer {
  display: flex;
  gap: 20px;
  padding: 0 20px;
  width: max-content;
  min-width: 100%;
}

.gameCard {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  width: 560px;
  height: 420px;
  transition: transform 0.3s ease;
  flex-shrink: 0;
}

.gameCard:hover {
  transform: translateY(-5px);
}