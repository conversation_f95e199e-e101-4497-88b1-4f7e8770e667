
/* FIRST FRAME - Blue background with logos and characters */
.firstFrame {
  background: linear-gradient(180deg, #2850FF 0%, #FFF 93.45%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 2rem 1rem;
  min-height: 60vh;
}

.logoX{
  color: white;
  font-weight: bolder;
  font-size: clamp(2rem, 4vw, 3rem);
  margin: 0 0.5rem;
}

.headerLogo {
  display: flex;
  gap: clamp(0.5rem, 2vw, 1rem);
  justify-content: center;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.headerLogo img {
  width: auto;
  height: clamp(60px, 8vw, 100px);
  max-width: clamp(200px, 25vw, 300px);
}

.charactersSection {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 1200px;
}

.charactersSection img {
  width: 100%;
  height: auto;
  max-width: 1000px;
  object-fit: contain;
}

/* SECOND FRAME - White background with text */
.secondFrame {
  background: #FFFFFF;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: clamp(2rem, 8vw, 5rem) clamp(1rem, 4vw, 2rem);
  text-align: left;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.mainHeading {
  font-size: clamp(2rem, 6vw, 4rem);
  font-weight: 700;
  font-family: var(--font-poppins);
  color: #EA73C0;
  margin: 0 0 1rem 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  text-align: left;
  line-height: 1.2;
}

.subHeading {
  font-size: clamp(1rem, 3vw, 1.8rem);
  font-family: var(--font-poppins);
  margin: 0 0 1.5rem 0;
  color: #949494;
  text-align: left;
  font-weight: 500;
}

.description {
  font-size: clamp(1rem, 2.5vw, 1.4rem);
  color: #86868B;
  font-family: var(--font-poppins);
  line-height: 1.8;
  margin: 0 0 2rem 0;
  width: 100%;
  text-align: left;
  font-weight: 600;
}

/* Teaser Videos Section */
.teaserTrailerHeading {
  font-size: clamp(1.5rem, 4vw, 3rem);
  font-weight: 700;
  font-family: var(--font-poppins);
  margin: clamp(2rem, 6vw, 2.5rem) 0 clamp(1rem, 3vw, 1.25rem) 0;
  text-align: center;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  width: 100%;
}

.teaserVideosContainer {
  display: flex;
  gap: clamp(0.5rem, 2vw, 1rem);
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
  justify-content: flex-start;
  box-sizing: border-box;
  padding-bottom: 1rem;
  margin-bottom: 2rem;
}

.teaserVideosContainer::-webkit-scrollbar {
  display: none;
}

.trailerVideoContainer {
  width: 100%;
  height: clamp(300px, 50vw, 746px);
  max-height: 746px;
  border: none;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  flex-shrink: 0;
  margin-bottom: 2rem;
}

.teaserVideoCard {
  border: none;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  width: clamp(280px, 60vw, 36rem);
  height: clamp(160px, 35vw, 20rem);
  flex-shrink: 0;
  min-width: 280px;
}


/* THIRD FRAME - Game screenshots */
.thirdFrame {
  padding: 0 0 clamp(2rem, 8vw, 5rem) 0;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.gamesContainer {
  display: flex;
  gap: clamp(0.75rem, 3vw, 1.25rem);
  padding: 0 clamp(1rem, 4vw, 1.25rem);
  width: max-content;
  min-width: 100%;
}

.gameCard {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  width: clamp(280px, 50vw, 560px);
  height: clamp(210px, 37.5vw, 420px);
  transition: transform 0.3s ease;
  flex-shrink: 0;
  min-width: 280px;
}

.gameCard:hover {
  transform: translateY(-5px);
}

.gameCard img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Button Container for responsive layout */
.buttonContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
  width: 100%;
  margin: 2rem 0;
}

/* RESPONSIVE BREAKPOINTS */

/* Mobile Phones (320px - 480px) */
@media only screen and (max-width: 480px) {
  .firstFrame {
    padding: 1.5rem 0.75rem;
    min-height: 50vh;
  }

  .headerLogo {
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }

  .headerLogo img {
    height: 50px;
    max-width: 180px;
  }

  .logoX {
    font-size: 1.5rem;
    margin: 0.25rem 0;
  }

  .secondFrame {
    padding: 1.5rem 1rem;
    align-items: center;
    text-align: center;
  }

  .mainHeading,
  .subHeading,
  .description {
    text-align: center;
  }

  .trailerVideoContainer {
    height: 200px;
  }

  .teaserVideoCard {
    width: 250px;
    height: 140px;
    min-width: 250px;
  }

  .gameCard {
    width: 250px;
    height: 188px;
    min-width: 250px;
  }

  .gamesContainer {
    gap: 0.5rem;
    padding: 0 0.75rem;
  }

  .buttonContainer {
    gap: 0.75rem;
    margin: 1.5rem 0;
  }
}

/* Tablets Portrait (481px - 768px) */
@media only screen and (min-width: 481px) and (max-width: 768px) {
  .firstFrame {
    padding: 2rem 1rem;
    min-height: 55vh;
  }

  .headerLogo img {
    height: 70px;
    max-width: 220px;
  }

  .logoX {
    font-size: 2rem;
  }

  .secondFrame {
    padding: 2.5rem 1.5rem;
  }

  .trailerVideoContainer {
    height: 350px;
  }

  .teaserVideoCard {
    width: 320px;
    height: 180px;
    min-width: 320px;
  }

  .gameCard {
    width: 320px;
    height: 240px;
    min-width: 320px;
  }
}

/* Tablets Landscape & Small Laptops (769px - 1024px) */
@media only screen and (min-width: 769px) and (max-width: 1024px) {
  .firstFrame {
    padding: 2.5rem 1.5rem;
    min-height: 60vh;
  }

  .headerLogo img {
    height: 80px;
    max-width: 250px;
  }

  .logoX {
    font-size: 2.5rem;
  }

  .secondFrame {
    padding: 3rem 2rem;
  }

  .trailerVideoContainer {
    height: 450px;
  }

  .teaserVideoCard {
    width: 400px;
    height: 225px;
    min-width: 400px;
  }

  .gameCard {
    width: 400px;
    height: 300px;
    min-width: 400px;
  }

  .buttonContainer {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1.5rem;
  }
}

/* Laptops & Desktops (1025px - 1440px) */
@media only screen and (min-width: 1025px) and (max-width: 1440px) {
  .firstFrame {
    padding: 3rem 2rem;
    min-height: 65vh;
  }

  .headerLogo img {
    height: 90px;
    max-width: 280px;
  }

  .logoX {
    font-size: 2.8rem;
  }

  .secondFrame {
    padding: 4rem 2.5rem;
  }

  .trailerVideoContainer {
    height: 600px;
  }

  .teaserVideoCard {
    width: 480px;
    height: 270px;
    min-width: 480px;
  }

  .gameCard {
    width: 480px;
    height: 360px;
    min-width: 480px;
  }

  .buttonContainer {
    flex-direction: row;
    justify-content: center;
    gap: 2rem;
  }
}

/* Large Desktops (1441px+) */
@media only screen and (min-width: 1441px) {
  .firstFrame {
    padding: 3rem 2rem;
    min-height: 70vh;
  }

  .headerLogo img {
    height: 100px;
    max-width: 300px;
  }

  .logoX {
    font-size: 3rem;
  }

  .secondFrame {
    padding: 5rem 3rem;
  }

  .trailerVideoContainer {
    height: 746px;
  }

  .teaserVideoCard {
    width: 576px;
    height: 324px;
    min-width: 576px;
  }

  .gameCard {
    width: 560px;
    height: 420px;
    min-width: 560px;
  }

  .buttonContainer {
    flex-direction: row;
    justify-content: center;
    gap: 2.5rem;
  }
}

/* Ultra-wide screens (1920px+) */
@media only screen and (min-width: 1920px) {
  .secondFrame {
    max-width: 1400px;
  }

  .firstFrame {
    min-height: 75vh;
  }
}